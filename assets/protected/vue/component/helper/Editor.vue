<template>
  <ckeditor v-model="editorData" :editor="editor" :config="editorConfig" v-bind="$attrs" />
</template>

<script>
import { Ckeditor, useCKEditorCloud } from '@ckeditor/ckeditor5-vue';
import {
  ClassicEditor,
  Essentials,
  Heading,
  Paragraph,
  Font,
  Bold,
  Italic,
  Underline,
  Strikethrough,
  Undo,
  Link,
  Image,
  Table,
  List,
  TableToolbar,
  ImageToolbar,
  ImageUpload,
  ImageStyle,
  ImageCaption,
  ImageResize,
  ImageInsert,
  HorizontalLine,
  SpecialCharacters,
  FontColor,
  FontBackgroundColor,
  SourceEditing,
  FindAndReplace,
  Indent,
  Alignment,
  Subscript,
  Superscript,
  RemoveFormat,
  Clipboard,
  SelectAll,
  SpecialCharactersEssentials,
} from 'ckeditor5';

import 'ckeditor5/ckeditor5.css';

export default {
  name: 'Editor',
  props: {
    value: {
      type: String,
      default: '',
    },
    preset: {
      type: String,
      default: 'basic',
      validator: (val) => ['basic', 'full'].includes(val),
    },
  },
  components: {
    ckeditor: CKEditor.component,
  },
  data() {
    return {
      editor: ClassicEditor,
      editorData: this.value,
    };
  },
  computed: {
    editorConfig() {
      const fullToolbar = [
        'undo',
        'redo',
        '|',
        'cut',
        'copy',
        '|',
        'bold',
        'italic',
        'underline',
        'strikethrough',
        'subscript',
        'superscript',
        '|',
        'removeFormat',
        '|',
        'findAndReplace',
        '|',
        'numberedList',
        'bulletedList',
        '|',
        'outdent',
        'indent',
        '|',
        'alignment:left',
        'alignment:center',
        'alignment:right',
        'alignment:justify',
        '|',
        'link',
        'unlink',
        '|',
        'insertImage',
        'insertTable',
        'horizontalLine',
        'specialCharacters',
        '|',
        'fontColor',
        'fontBackgroundColor',
        '|',
        'sourceEditing',
        '|',
        'maximize',
      ];

      const basicToolbar = [
        'heading',
        '|',
        'undo',
        'redo',
        '|',
        'bold',
        'italic',
        'underline',
        '|',
        'numberedList',
        'bulletedList',
        '|',
        'insertImage',
        'insertTable',
      ];

      return {
        licenseKey: 'GPL',
        plugins: [
          Essentials,
          Heading,
          Paragraph,
          Font,
          Bold,
          Italic,
          Underline,
          Strikethrough,
          Undo,
          Link,
          Image,
          Table,
          HorizontalLine,
          SpecialCharacters,
          FontColor,
          FontBackgroundColor,
          SourceEditing,
          FindAndReplace,
          Indent,
          Alignment,
          Subscript,
          Superscript,
          RemoveFormat,
          List,
          TableToolbar,
          ImageToolbar,
          ImageUpload,
          ImageStyle,
          ImageCaption,
          ImageResize,
          ImageInsert,
          Clipboard,
          SelectAll,
          SpecialCharactersEssentials,
        ],
        toolbar: this.preset === 'full' ? fullToolbar : basicToolbar,
      };
    },
  },
  watch: {
    value(newVal) {
      if (newVal !== this.editorData) {
        this.editorData = newVal;
      }
    },
    editorData(newVal) {
      this.$emit('input', newVal); // v-model compatibility
    },
  },
};
</script>
