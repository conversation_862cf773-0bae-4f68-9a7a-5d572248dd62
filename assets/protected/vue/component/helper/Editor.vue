<template>
  <ckeditor v-if="editor" v-model="data" :editor="editor" :config="config" v-bind="$attrs" />
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { Ckeditor, useCKEditorCloud } from '@ckeditor/ckeditor5-vue';

interface Props {
  value?: string;
  preset?: 'basic' | 'full';
}

const props = withDefaults(defineProps<Props>(), {
  value: '',
  preset: 'basic',
});

const emit = defineEmits<{
  input: [value: string];
}>();

const cloud = useCKEditorCloud({
  version: '46.1.0',
  premium: false,
});

const data = ref(props.value);
const editor = computed(() => {
  if (!cloud.data.value) {
    return null;
  }

  return cloud.data.value.CKEditor.ClassicEditor;
});

const config = computed(() => {
  if (!cloud.data.value) {
    return null;
  }

  const {
    Essentials,
    Heading,
    Paragraph,
    Font,
    Bold,
    Italic,
    Underline,
    Strikethrough,
    Undo,
    Link,
    Image,
    Table,
    List,
    TableToolbar,
    ImageToolbar,
    ImageUpload,
    ImageStyle,
    ImageCaption,
    ImageResize,
    ImageInsert,
    HorizontalLine,
    SpecialCharacters,
    FontColor,
    FontBackgroundColor,
    SourceEditing,
    FindAndReplace,
    Indent,
    Alignment,
    Subscript,
    Superscript,
    RemoveFormat,
    Clipboard,
    SelectAll,
    SpecialCharactersEssentials,
  } = cloud.data.value.CKEditor;

  const fullToolbar = [
    'undo',
    'redo',
    '|',
    'cut',
    'copy',
    '|',
    'bold',
    'italic',
    'underline',
    'strikethrough',
    'subscript',
    'superscript',
    '|',
    'removeFormat',
    '|',
    'findAndReplace',
    '|',
    'numberedList',
    'bulletedList',
    '|',
    'outdent',
    'indent',
    '|',
    'alignment:left',
    'alignment:center',
    'alignment:right',
    'alignment:justify',
    '|',
    'link',
    'unlink',
    '|',
    'insertImage',
    'insertTable',
    'horizontalLine',
    'specialCharacters',
    '|',
    'fontColor',
    'fontBackgroundColor',
    '|',
    'sourceEditing',
    '|',
    'maximize',
  ];

  const basicToolbar = [
    'heading',
    '|',
    'undo',
    'redo',
    '|',
    'bold',
    'italic',
    'underline',
    '|',
    'numberedList',
    'bulletedList',
    '|',
    'insertImage',
    'insertTable',
  ];

  return {
    licenseKey: 'GPL',
    plugins: [
      Essentials,
      Heading,
      Paragraph,
      Font,
      Bold,
      Italic,
      Underline,
      Strikethrough,
      Undo,
      Link,
      Image,
      Table,
      HorizontalLine,
      SpecialCharacters,
      FontColor,
      FontBackgroundColor,
      SourceEditing,
      FindAndReplace,
      Indent,
      Alignment,
      Subscript,
      Superscript,
      RemoveFormat,
      List,
      TableToolbar,
      ImageToolbar,
      ImageUpload,
      ImageStyle,
      ImageCaption,
      ImageResize,
      ImageInsert,
      Clipboard,
      SelectAll,
      SpecialCharactersEssentials,
    ],
    toolbar: props.preset === 'full' ? fullToolbar : basicToolbar,
  };
});

// Watch for prop changes
watch(
  () => props.value,
  (newVal) => {
    if (newVal !== data.value) {
      data.value = newVal;
    }
  },
);

// Watch for data changes and emit
watch(data, (newVal) => {
  emit('input', newVal); // v-model compatibility
});
</script>
